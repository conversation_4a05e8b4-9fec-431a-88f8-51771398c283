import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Avatar,
  Tabs,
  Tab,
  Card,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Grid
} from '@mui/material';
import { Refresh, AccountCircle } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import EmailList from './EmailList';
import AppPage from '../../components/App/AppPage';
import { DashboardProps, Message, Activity, Reminder } from '../../types';

const Dashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const { t } = useTranslation();
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<number>(0);

  const mockActivities: Activity[] = [
    {
      id: 1,
      title: 'Picture Day - Lincoln Elementary',
      category: 'school_event',
      priority: 'medium',
      date: '2024-02-15',
      child: 'Emma',
      status: 'upcoming',
      description: 'School picture day. Remember to dress Emma in her blue dress.'
    },
    {
      id: 2,
      title: 'Soccer Practice Cancelled',
      category: 'sports',
      priority: 'high',
      date: '2024-02-12',
      child: 'Jake',
      status: 'urgent',
      description: 'Due to weather conditions, soccer practice is cancelled today.'
    }
  ];

  const mockReminders: Reminder[] = [
    { id: 1, text: 'Permission slip for field trip due tomorrow', priority: 'high' },
    { id: 2, text: 'Bring snacks for Emma\'s class party on Friday', priority: 'medium' }
  ];

  const fetchMessages = async (): Promise<void> => {
    setLoading(true);
    try {
      const response = await fetch('/api/email/messages', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data: Message[] = await response.json();
        setMessages(data);
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      setMessages([
        {
          id: '1',
          from: 'Lincoln Elementary <<EMAIL>>',
          subject: 'Picture Day Reminder - February 15th',
          snippet: 'Don\'t forget! Picture day is coming up on February 15th...',
          date: '2024-02-10'
        }
      ]);
    }
    setLoading(false);
  };

  const fetchMessage = async (messageId: string): Promise<void> => {
    const mockMessage: Message = {
      id: messageId,
      from: 'Lincoln Elementary <<EMAIL>>',
      subject: 'Picture Day Reminder - February 15th',
      snippet: 'This is a friendly reminder that Picture Day is scheduled for Thursday, February 15th...',
      date: '2024-02-10',
      body: 'Dear Parents,\n\nThis is a friendly reminder that Picture Day is scheduled for Thursday, February 15th.\n\nPlease ensure your child:\n- Arrives on time\n- Is dressed appropriately\n- Has their hair combed\n\nPhotos will be taken between 9:00 AM and 11:00 AM.\n\nThank you,\nLincoln Elementary Staff'
    };
    setSelectedMessage(mockMessage);
  };

  useEffect(() => {
    fetchMessages();
  }, []);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      default: return 'success';
    }
  };

  const renderOverview = () => (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          { icon: '📧', number: '12', label: t('dashboard.newMessages') },
          { icon: '⏰', number: '5', label: t('dashboard.upcomingEvents') },
          { icon: '🚨', number: '2', label: t('dashboard.urgentItems') },
          { icon: '👨👩👧👦', number: '2', label: t('dashboard.children') }
        ].map((stat, index) => (
          <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h2" sx={{ fontSize: '2rem', mb: 1 }}>{stat.icon}</Typography>
                <Typography variant="h4" component="div">{stat.number}</Typography>
                <Typography variant="body2" color="text.secondary">{stat.label}</Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>{t('dashboard.recentActivities')}</Typography>
              {mockActivities.map(activity => (
                <Card key={activity.id} variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="h6">{activity.title}</Typography>
                      <Chip 
                        label={activity.priority} 
                        color={getPriorityColor(activity.priority) as any}
                        size="small"
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      👤 {activity.child} • 📅 {activity.date}
                    </Typography>
                    <Typography variant="body2">{activity.description}</Typography>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, md: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>{t('dashboard.quickReminders')}</Typography>
              <List>
                {mockReminders.map(reminder => (
                  <ListItem key={reminder.id} divider>
                    <ListItemText primary={reminder.text} />
                    <ListItemSecondaryAction>
                      <Button size="small" variant="outlined">{t('dashboard.done')}</Button>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  return (
    <AppPage user={user} onLogout={onLogout} title="Dashboard">
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label={`📊 ${t('dashboard.overview')}`} />
          <Tab label={`📧 ${t('dashboard.messages')}`} />
          <Tab label={`📅 ${t('dashboard.calendar')}`} />
          <Tab label={`👨👩👧👦 ${t('dashboard.family')}`} />
        </Tabs>
      </Box>

      {activeTab === 0 && renderOverview()}

      {activeTab === 1 && (
        <Box sx={{ p: 3 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5">{t('dashboard.recentMessages')}</Typography>
            <Button
              variant="contained"
              startIcon={<Refresh />}
              onClick={fetchMessages}
              disabled={loading}
            >
              {loading ? t('dashboard.loading') : t('dashboard.refresh')}
            </Button>
          </Box>

          <Grid container spacing={3}>
            <Grid size={{ xs: 12, md: 6 }}>
              <EmailList
                messages={messages}
                onSelectMessage={fetchMessage}
                selectedMessageId={selectedMessage?.id}
              />
            </Grid>

            {selectedMessage && (
              <Grid size={{ xs: 12, md: 6 }}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>{t('dashboard.messageDetails')}</Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>{t('dashboard.from')}:</strong> {selectedMessage.from}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>{t('dashboard.subject')}:</strong> {selectedMessage.subject}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>{t('dashboard.date')}:</strong> {selectedMessage.date}
                    </Typography>
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                        {selectedMessage.body || selectedMessage.snippet}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            )}
          </Grid>
        </Box>
      )}

      {activeTab === 2 && (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Card>
            <CardContent>
              <Typography variant="h2" sx={{ fontSize: '4rem', mb: 2 }}>📅</Typography>
              <Typography variant="h5" gutterBottom>Calendar View</Typography>
              <Typography variant="body1" color="text.secondary">
                Calendar integration coming soon. View all your family's events and deadlines in one place.
              </Typography>
            </CardContent>
          </Card>
        </Box>
      )}

      {activeTab === 3 && (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Card>
            <CardContent>
              <Typography variant="h2" sx={{ fontSize: '4rem', mb: 2 }}>👨👩👧👦</Typography>
              <Typography variant="h5" gutterBottom>Family Management</Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                Manage family members, children profiles, and sharing settings.
              </Typography>
              <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(2, 1fr)' }, gap: 2, justifyContent: 'center', maxWidth: 600, mx: 'auto' }}>
                {[
                  { name: 'Emma (Age 8)', school: 'Lincoln Elementary - 3rd Grade' },
                  { name: 'Jake (Age 12)', school: 'Roosevelt Middle School - 7th Grade' }
                ].map((child, index) => (
                  <Card key={index} variant="outlined">
                    <CardContent>
                      <Avatar sx={{ width: 56, height: 56, mx: 'auto', mb: 2 }}>
                        <AccountCircle />
                      </Avatar>
                      <Typography variant="h6">{child.name}</Typography>
                      <Typography variant="body2" color="text.secondary">{child.school}</Typography>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}
    </AppPage>
  );
};

export default Dashboard;